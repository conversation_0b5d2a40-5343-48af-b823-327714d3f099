@extends('website.layout.master')
@push('css')
    <style>
    </style>
@endpush
@section('content')
    <section class="faq py-5 help_center detail_sec">
        <div class="container">
            <div class="row overflow-hidden">
                <div class="col-md-12">
                    <div class="d-flex justify-content-between align-items-center py-1">
                        <div class="search_parent w-100">
                            <form action="{{ route('faq') }}" method="GET" class="search d-flex ms-3 pe-3 py-1">
                                <input type="text" id="search-listing-inp" name="search" value="{{ request()->search }}"
                                    class="form-control" placeholder="{{ translate('help_center_detail.search_for_answer') }}">
                                <button class="btn"><i class="fa fa-search dark-yellow"></i></button>
                            </form>
                        </div>
                        <a href="{{ route('help_center') }}" class="reset_btn btn_black button">{{ translate('help_center_detail.reset') }}</a>
                    </div>
                </div>
            </div>
            <div class="row mt-5 box_parent">
                <div class="col-md-12">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('help_center') }}">{{ translate('help_center_detail.help_center') }}</a></li>
                            <li class="breadcrumb-item active" aria-current="page">{{ $help_center->title }}</li>
                        </ol>
                    </nav>
                </div>
                <div class="col-md-3 box_category">
                    <div class="card b_shadow fixed">
                        <div class="card-header text-end border-0 bg_white">
                            <i class="far fa-file-alt"></i>
                            {{ count($help_center->faqs) }}
                        </div>
                        <div class="card-body text-center">
                            <h5 class="card-title ">
                                {{ $help_center->title }}
                                </h3>
                                <p class="small text-black-50">{{ translate('help_center_detail.last_updated') }}: {{ $help_center->updated_at->diffForHumans() }}</p>
                        </div>
                        <div class="card-footer border-0 bg_white">
                            {!! $help_center->description !!}
                        </div>
                    </div>
                </div>
                <div class="col-md-9 faq_parent">
                    @forelse ($help_center->faqs as $faq)
                        <a href="{{ route('faq_detail', $faq->slug) }}" style="text-decoration: none">
                            <div class="card faq_card b_dark_shadow" data-aos="fade-left" data-aos-delay="500">
                                <div class="card-body d-flex justify-content-between align-items-center">
                                    <p class="mb-0">{{ $faq->title }}</p>
                                    <i class="fas fa-chevron-right" style="font-size: 30px"></i>
                                </div>
                            </div>
                        </a>
                    @empty
                        <h6 class="text-center">{{ translate('help_center_detail.faq_not_found') }}</h6>
                    @endforelse
                </div>

            </div>
        </div>
    </section>
@endsection
@push('js')
    <script>
        $(document).ready(function() {
            // Initially set box view
            $('#view-container').addClass('box-view');

            // Toggle to list view
            $('#list-view').click(function() {
                // Remove box-view and add list-view
                $('#view-container').removeClass('box-view').addClass('list-view');

                // Change child items to col-12 (full width)
                $('#view-container .box-view-item').removeClass('col-12 col-md-4').addClass('col-12');

                // Disable the list view button and enable the box view button
                $('#list-view').prop('disabled', true);
                $('#box-view').prop('disabled', false);
            });

            // Toggle to box view
            $('#box-view').click(function() {
                // Remove list-view and add box-view
                $('#view-container').removeClass('list-view').addClass('box-view');

                // Change child items to col-4 (3 items per row)
                $('#view-container .box-view-item').removeClass('col-12').addClass('col-12 col-md-4');

                // Disable the box view button and enable the list view button
                $('#box-view').prop('disabled', true);
                $('#list-view').prop('disabled', false);
            });
        });
    </script>
@endpush
