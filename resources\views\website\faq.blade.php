@extends('website.layout.master')
@section('content')
    <section class="faq py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="search_parent w-100">
                            <form action="{{ route('faq') }}" method="GET" class="search d-flex ms-3 pe-3 py-1">
                                <input type="text" id="search-listing-inp" name="search" value="{{ request()->search }}"
                                    class="form-control" placeholder="What are you looking for?">
                                <button class="btn"><i class="fa fa-search dark-yellow"></i></button>
                            </form>
                        </div>
                        <a href="{{ route('help_center') }}" class="reset_btn btn_black button">Reset</a>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-12">
                    <div class="about-box2">
                        <h2 class="py-4 ms-3" data-aos="fade">Help Center</h2>
                        @if(request()->search)
                            <p class="ms-3 pb-3" data-aos="fade-up" data-aos-delay="200">
                                <strong>Search results for:</strong> "{{ request()->search }}"
                                ({{ $faqs->count() }} {{ $faqs->count() == 1 ? 'result' : 'results' }} found)
                            </p>
                        @endif
                    </div>
                    <div>
                        @forelse ($faqs as $faq)
                            <a href="{{ route('faq_detail', $faq->slug) }}" style="text-decoration: none">
                                <div class="faq_sec " data-aos="fade" data-aos-delay="500">
                                    <div class="card-body d-flex justify-content-between align-items-center">
                                        <p class="mb-0">{{ $faq->title }}</p>
                                        <i class="fas fa-chevron-right" style="font-size: 30px"></i>
                                    </div>
                                </div>
                            </a>
                        @empty
                            <h6 class="text-center" data-aos="fade" data-aos-delay="100">Sorry, Nothing Found!</h6>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
