@extends('website.layout.master')
@push('css')
    <style>
        .empty_listing {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 30vh;
            font-size: 30px;
            font-weight: bold;
        }

        .loader {
            border: 16px solid #f3f3f3;
            border-radius: 50%;
            border-top: 16px solid #171717;
            ;
            width: 120px;
            height: 120px;
            -webkit-animation: spin 1s linear infinite;
            /* Safari */
            animation: spin 1s linear infinite;
            display: block;
            margin: 150px auto;
        }


        [tooltip] {
            position: relative;
        }

        [tooltip]::before,
        [tooltip]::after {
            text-transform: none;
            font-size: .9em;
            line-height: 1;
            user-select: none;
            pointer-events: none;
            position: absolute;
            display: none;
            opacity: 0;
        }

        [tooltip]::before {
            content: '';
            border: 5px solid transparent;
            z-index: 1001;
        }

        [tooltip]::after {
            content: attr(tooltip);
            font-family: Helvetica, sans-serif;
            text-align: center;
            min-width: 3em;
            max-width: 21em;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: 1ch 1.5ch;
            border-radius: .3ch;
            box-shadow: 0 1em 2em -.5em rgba(0, 0, 0, 0.35);
            background: #333;
            color: #fff;
            z-index: 1000;
        }

        [tooltip]:hover::before,
        [tooltip]:hover::after {
            display: block;
        }

        [tooltip='']::before,
        [tooltip='']::after {
            display: none !important;
        }

        [tooltip]:not([flow])::before,
        [tooltip][flow^="up"]::before {
            bottom: 100%;
            border-bottom-width: 0;
            border-top-color: #333;
        }

        [tooltip]:not([flow])::after,
        [tooltip][flow^="up"]::after {
            bottom: calc(100% + 5px);
        }

        [tooltip]:not([flow])::before,
        [tooltip]:not([flow])::after,
        [tooltip][flow^="up"]::before,
        [tooltip][flow^="up"]::after {
            left: 50%;
            transform: translate(-50%, -.5em);
        }

        [tooltip][flow^="down"]::before {
            top: 100%;
            border-top-width: 0;
            border-bottom-color: #333;
        }

        [tooltip][flow^="down"]::after {
            top: calc(100% + 5px);
        }

        [tooltip][flow^="down"]::before,
        [tooltip][flow^="down"]::after {
            left: 50%;
            transform: translate(-50%, .5em);
        }

        [tooltip][flow^="left"]::before {
            top: 50%;
            border-right-width: 0;
            border-left-color: #333;
            left: calc(0em - 5px);
            transform: translate(-.5em, -50%);
        }

        [tooltip][flow^="left"]::after {
            top: 50%;
            right: calc(100% + 5px);
            transform: translate(-.5em, -50%);
        }

        [tooltip][flow^="right"]::before {
            top: 50%;
            border-left-width: 0;
            border-right-color: #333;
            right: calc(0em - 5px);
            transform: translate(.5em, -50%);
        }

        [tooltip][flow^="right"]::after {
            top: 50%;
            left: calc(100% + 5px);
            transform: translate(.5em, -50%);
        }

        @keyframes tooltips-vert {
            to {
                opacity: .9;
                transform: translate(-50%, 0);
            }
        }

        @keyframes tooltips-horz {
            to {
                opacity: .9;
                transform: translate(0, -50%);
            }
        }

        [tooltip]:not([flow]):hover::before,
        [tooltip]:not([flow]):hover::after,
        [tooltip][flow^="up"]:hover::before,
        [tooltip][flow^="up"]:hover::after,
        [tooltip][flow^="down"]:hover::before,
        [tooltip][flow^="down"]:hover::after {
            animation: tooltips-vert 300ms ease-out forwards;
        }

        [tooltip][flow^="left"]:hover::before,
        [tooltip][flow^="left"]:hover::after,
        [tooltip][flow^="right"]:hover::before,
        [tooltip][flow^="right"]:hover::after {
            animation: tooltips-horz 300ms ease-out forwards;
        }



        /* Safari */
        @-webkit-keyframes spin {
            0% {
                -webkit-transform: rotate(0deg);
            }

            100% {
                -webkit-transform: rotate(360deg);
            }
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
@endpush
@section('content')
    <section class="bookings">
        <div class="container">
            <div class="row">
                <div class="col-md-12 pt-5 pb-3 booking_btns">
                    <h2>{{ translate('user_bookings.my_bookings') }}</h2>
                    <div class="past-current">
                        <a class="button " id="past-booking-btn" href="#">{{ translate('user_bookings.past_booking') }}</a>
                        <a class="button style-past-booking" id="current-booking-btn"
                            href="#">{{ translate('user_bookings.on_going_booking') }}</a>
                    </div>
                </div>
                {{-- <div class="col-md-3"> --}}
                {{-- <div class="filter_box">
                        <div class="dropdown">
                            <button class="btn button" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown"
                                aria-expanded="false">
                                Filter <i class="bi bi-funnel"></i>
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                <li><a class="dropdown-item" href="#">Action</a></li>
                                <li><a class="dropdown-item" href="#">Another</a></li>
                                <li><a class="dropdown-item" href="#">Something</a></li>
                            </ul>
                        </div>
                        <div class="dropdown">
                            <button class="btn button" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown"
                                aria-expanded="false">
                                Sort <i class="bi bi-chevron-down"></i>
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                <li><a class="dropdown-item" href="#">Action</a></li>
                                <li><a class="dropdown-item" href="#">Another</a></li>
                                <li><a class="dropdown-item" href="#">Something</a></li>
                            </ul>
                        </div>
                    </div> --}}
                {{-- </div> --}}
                <div class="col-md-5 offset-md-7">
                    <div class="search d-flex px-3 py-2 justify-content-between">
                        <input type="text" class="form-control" placeholder="{{ translate('user_bookings.search_here') }}" id="searchInput">
                        <button class="btn search_reset"><i class="fa fa-times" style="color: #4A4A4A;"></i></button>
                        <button class="btn search_booking"><i class="fa fa-search" style="color: #4A4A4A;"></i></button>
                    </div>
                </div>
            </div>
            {{-- loader --}}
            <div id="listing-loading">
                <div class="loader"></div>
            </div>
            {{-- loader end --}}
            <div class="row booking_card_wrapper" id="booking-card-container">

            </div>
        </div>
    </section>
    @include('website.layout.review')

    {{-- edit modal --}}
    <div class="modal fade review" id="review_edit_modal" tabindex="-1" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-all ">
            <form action="{{ route('review_update') }}" enctype="multipart/form-data" method="POST">
                <div class="modal-dialog ">
                    <div class="modal-content">
                        @csrf
                        <div class="modal-header justify-content-center">
                            <h3 class="modal-title text-center" id="exampleModalLabel">
                                {{ translate('user_bookings.edit_review') }}
                            </h3>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body mb-3">

                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    {{-- edit modal end --}}

    <!-- Button trigger modal -->
    {{-- <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#report_modal">
        Launch demo modal
    </button> --}}

    <!-- Modal -->
    <div class="modal report fade" id="report_modal" tabindex="-1" aria-labelledby="reportHeading" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title mx-auto text-center" id="reportHeading">{{ translate('user_bookings.report_listing') }} <span class="listing"></span></h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="{{ route('report_form') }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        <input type="hidden" name="booking_id" id="booking_id">
                        <div class="mb-3">
                            <input type="text" name="subject" class="form-control" placeholder="{{ translate('user_bookings.subject') }}">
                        </div>
                        <div class="mb-3">
                            <textarea class="form-control" name="description" rows="7" id="" placeholder="{{ translate('user_bookings.description') }}"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        {{-- <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button> --}}
                        <button type="submit" class="bg_black btn button1 white">{{ translate('user_bookings.submit') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection


@push('js')
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>

    <script>
        $(document).ready(function() {
            // get past booking
            let isPastBookingPregress = false;
            const get_past_booking = () => {
                if(isPastBookingPregress) return;
                isPastBookingPregress = true;
                $('#listing-loading').show();
                $("#booking-card-container").empty();
                $(".bookings .search input").val('');
                $(".bookings .search .search_booking").show();
                $(".bookings .search .search_reset").hide();
                $.ajax({
                    url: "{{ route('get_past_booking') }}",
                    type: "GET",
                    success: function(response) {
                        // $("#booking-card-container").empty();
                        if (response.status == true) {
                            // $("#booking-card-container").empty();
                            $("#booking-card-container").append(response.data);
                        } else {
                            // $("#booking-card-container").empty();
                            // $(".Load_more").hide();
                            $("#booking-card-container").append(
                                `  <div class="empty_listing">
                                        ${response.message}
                                    </div>
                                `
                            );
                        }
                    },
                    complete: function() {
                        isPastBookingPregress = false;
                        $('#listing-loading').hide();
                    }
                });
            };

            let isRequestInProgress = false;
            // get current booking
            const get_current_booking = () => {
                if (isRequestInProgress) return;
                isRequestInProgress = true;
                $('#listing-loading').show();
                $("#booking-card-container").empty();
                $(".bookings .search input").val('');
                $(".bookings .search .search_booking").show();
                $(".bookings .search .search_reset").hide();
                $.ajax({
                    url: "{{ route('get_current_booking') }}",
                    type: "GET",
                    success: function(response) {
                        if (response.status == true) {
                            $("#booking-card-container").html(response.data);
                        } else {
                            $(".Load_more").hide();
                            $("#booking-card-container").html(
                                `<div class="empty_listing">
                                    ${response.message}
                                </div>`
                            );
                        }
                    },
                    complete: function() {
                        isRequestInProgress = false;
                        $('#listing-loading').hide();
                    }
                });
            };

            get_current_booking();

            $("#current-booking-btn").on("click", function() {
                $(this).addClass("style-past-booking");
                $("#past-booking-btn").removeClass("style-past-booking");
                get_current_booking();
            });

            $("#past-booking-btn").on("click", function() {
                $(this).addClass("style-past-booking");
                $("#current-booking-btn").removeClass("style-past-booking");
                get_past_booking();
            });

        });
    </script>
    <script>
        $(document).ready(function() {
            $(document).on("click", ".report-btn", function() {
                let booking_id = $(this).attr("data-booking-id");
                let listing = $(this).closest('.main_cart').find(".listing_name").text();
                $("#booking_id").val(booking_id);
                $(".listing").text(listing);
            })

            $("body").on("click", '.search_booking', function() {
                var value = $(this).closest('.search').find('input').val().toLowerCase();
                searchBooking(value);

            });
            $("body").on("keyup", '.search input', function() {
                var value = $(this).closest('.search').find('input').val().toLowerCase();
                if (event.keyCode === 13) {
                    searchBooking(value);
                }
            });
            $("body").on("input", '#searchInput', function() {
                var value = $(this).val().toLowerCase(); // Get the current value of input field
                searchBooking(value); // Call the search function with the current value
            });


            $("body").delegate('.search_reset', "click", function() {
                var value = $(this).closest('.search').find('input').val('');
                var rowCount = 0;

                $(this).hide();
                $('.search_booking').show();

                $(".booking_card_wrapper .booking_cards_parent").each(function() {
                    $(this).removeAttr('style');
                });

                $('.no-data-found').remove();
            });

            //Updated searchBooking function
            function searchBooking(value) {
                var rowCount = 0;
                $('#listing-loading').show();

                setTimeout(function() {
                    $(".booking_card_wrapper .booking_cards_parent").filter(function() {
                        var bookingId = $(this).find('[data-booking-id]').attr('data-booking-id') ||
                            "";
                        var hostName = $(this).find('.host_name').text().toLowerCase() || "";
                        var listingName = $(this).find('.listing_name').text().toLowerCase() || "";
                        var match = false;

                        // Search for the numeric part of the booking ID as well as the full booking ID
                        var searchTerms = value.split(
                            ' '); // Split search value into multiple terms
                        searchTerms.forEach(function(term) {
                            // Checking if the search term matches with the booking ID, Host Name, or Listing Name
                            if (bookingId.toLowerCase().indexOf(term) > -1 || hostName
                                .indexOf(term) > -1 || listingName.indexOf(term) > -1) {
                                match = true;
                            }

                            // If only the numeric part of the booking ID is typed, we match against the numeric part
                            if (term.match(/^\d+$/) && bookingId.replace(/\D/g, '').indexOf(
                                    term) > -1) {
                                match = true;
                            }
                        });

                        $(this).toggle(match); // Show/hide element based on match

                        if (match) rowCount++;
                    });

                    console.log("Total matches:", rowCount);

                    if (rowCount === 0) {
                        if ($('.booking_card_wrapper').find('.no_found_data').length == 0) {
                            $(".booking_card_wrapper").append(
                                '<h3 class="no_found_data pt-5 text-center bold"><span class="no-data-found">{{ translate('user_bookings.no_booking_found') }}</span></h3>'
                            );
                        }
                    } else {
                        $('.no_found_data').remove();
                    }
                    $('.search_reset').show();
                    $('.search_booking').hide();
                    $('#listing-loading').hide();
                }, 300);
            }





            let loader = `<div id="listing-loading"><div class="loader"></div></div>`;
            $(document).on("click", ".review-btn", function() {
                let booking_id = $(this).attr("data-booking-id");
                $("#review .modal-body").html(loader);
                $.ajax({
                    url: "{{ route('get_review_form') }}",
                    type: "GET",
                    data: {
                        booking_id: booking_id
                    },
                    success: function(response) {
                        if (response.status == true) {
                            $("#review .modal-body").html(response.data);
                        } else {
                            Swal.fire({
                                title: "Error!",
                                text: response.message,
                                icon: "error"
                            });
                        }
                        window.validateRatings();
                        $(document).on("change", ".rating-checkbox", window.validateRatings);

                    }
                });
            })

            // edit review
            $(document).on("click", ".edit-review-btn", function() {
                let review_id = $(this).attr("data-review-id");
                $("#review_edit_modal .modal-body").html(loader);
                if (review_id) {
                    $.ajax({
                        url: `{{ route('edit_review_form', '') }}/${review_id}`,
                        type: "GET",
                        success: function(response) {
                            if (response.status == true) {
                                $("#review_edit_modal .modal-body").html(response.data);
                                $("#review_edit_modal").modal('show');
                            } else {
                                Swal.fire({
                                    title: "Error!",
                                    text: response.message,
                                    icon: "error"
                                });
                            }

                            window.validateRatings();
                            $(document).on("change", ".rating-checkbox", window
                                .validateRatings);
                        }
                    });
                }
            })
            // edit review end
        });
    </script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.15.6/Sortable.min.js"></script>

    <script>
        // $(document).ready(function() {
        //     const sortableContainer = $('.drag_drop_photos_wrapper');
        //     new Sortable(sortableContainer[0], {
        //         animation: 150,
        //         draggable: '.sortable-element',
        //         onEnd: function() {
        //             const image_ids = sortableContainer
        //                 .children('.sortable-element')
        //                 .map(function() {
        //                     return $(this).data('image-id');
        //                 })
        //                 .get();
        //         },
        //     });
        // });

        // $(document).on('change', '#add_photo_file', function(event) {
        //     const files = event.target.files;
        //     if (files.length === 0) {
        //         alert("No files selected.");
        //         return;
        //     }

        //     const sortableContainer = $('.drag_drop_photos_wrapper');

        //     Swal.fire({
        //         title: "Uploading Images",
        //         html: `Uploading <b>0</b> out of ${files.length}`,
        //         allowOutsideClick: false,
        //         showConfirmButton: false,
        //         didOpen: () => {
        //             Swal.showLoading();
        //         },
        //     });

        //     let uploadedCount = 0;
        //     const totalFiles = files.length;

        //     for (let i = 0; i < files.length; i++) {
        //         let file = files[i];
        //         let reader = new FileReader();

        //         reader.onload = function(e) {
        //             uploadedCount++;
        //             Swal.update({
        //                 html: `Uploading <b>${uploadedCount}</b> out of ${totalFiles}...`,
        //             });

        //             const newDiv = $(
        //                 `<div class="drag_drop_photo_single sortable-element">
    //                     <img alt="Preview ${i}" loading="lazy" src="${e.target.result}">
    //                     <div class="delete_btn_wrapper">
    //                         <a class="delete_btn" href="#"><i class="fa fa-trash" aria-hidden="true"></i></a>
    //                     </div>
    //                 </div>`
        //             );
        //             const addPhotoBox = $(
        //                 '.drag_drop_photo_single.add_photo_box'
        //             );
        //             newDiv.insertBefore(addPhotoBox);
        //         };

        //         reader.readAsDataURL(file);
        //     }

        //     setTimeout(() => {
        //         if (sortableContainer.length && typeof Sortable !== "undefined") {
        //             new Sortable(sortableContainer[0], {
        //                 animation: 150,
        //                 draggable: '.sortable-element',
        //                 onEnd: function() {
        //                     const image_ids = sortableContainer
        //                         .children('.sortable-element')
        //                         .map(function() {
        //                             return $(this).data('image-id');
        //                         })
        //                         .get();
        //                 },
        //             });
        //         }
        //     }, 500);

        //     Swal.fire({
        //         title: "Upload Complete",
        //         text: "All images have been uploaded successfully!",
        //         icon: "success",
        //         timer: 1500,
        //         showConfirmButton: false
        //     });
        // });

        // $(document).on('change', '#add_photo_file', function(event) {
        //     const files = event.target.files;

        //     const validTypes = ['image/jpeg', 'image/jpg', 'image/png'];
        //     const minSize = 300 * 1024;
        //     const maxSize = 10 * 1024 * 1024; // 10MB
        //     const maxFiles = 10; // number of images per upload
        //     let validImages = []; // Store valid images
        //     let validImageCount = 0;

        //     if (files.length === 0) {
        //         alert("No files selected.");
        //         return;
        //     }

        //     const sortableContainer = $('.drag_drop_photos_wrapper');

        //     Swal.fire({
        //         title: "Uploading Images",
        //         html: `Uploading <b>0</b> out of ${files.length}`,
        //         allowOutsideClick: false,
        //         showConfirmButton: false,
        //         didOpen: () => {
        //             Swal.showLoading();
        //         },
        //     });

        //     let uploadedCount = 0;
        //     const totalFiles = files.length;

        //     const resizeImage = (file, maxWidth = 300, maxHeight = 300) => {
        //         return new Promise((resolve) => {
        //             const reader = new FileReader();
        //             reader.onload = function(e) {
        //                 const img = new Image();
        //                 img.onload = function() {
        //                     let canvas = document.createElement("canvas");
        //                     let ctx = canvas.getContext("2d");

        //                     let ratio = Math.min(maxWidth / img.width, maxHeight / img.height);
        //                     canvas.width = img.width * ratio;
        //                     canvas.height = img.height * ratio;

        //                     ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        //                     resolve(canvas.toDataURL("image/jpeg", 0.7));
        //                 };
        //                 img.src = e.target.result;
        //             };
        //             reader.readAsDataURL(file);
        //         });
        //     };

        //     const processFiles = async () => {
        //         for (let i = 0; i < files.length; i++) {
        //             let resizedDataUrl = await resizeImage(files[i]);

        //             uploadedCount++;
        //             Swal.update({
        //                 html: `Uploading <b>${uploadedCount}</b> out of ${totalFiles}...`,
        //             });

        //             const newDiv = $(`
    //                 <div class="drag_drop_photo_single sortable-element">
    //                     <img alt="Preview ${i}" loading="lazy" src="${resizedDataUrl}">
    //                     <div class="delete_btn_wrapper">
    //                         <a class="delete_btn" href="#"><i class="fa fa-trash" aria-hidden="true"></i></a>
    //                     </div>
    //                 </div>
    //             `);

        //             const addPhotoBox = $('.drag_drop_photo_single.add_photo_box');
        //             newDiv.insertBefore(addPhotoBox);
        //         }

        //         setTimeout(() => {
        //             if (sortableContainer.length && typeof Sortable !== "undefined") {
        //                 new Sortable(sortableContainer[0], {
        //                     animation: 150,
        //                     draggable: '.sortable-element',
        //                     onEnd: function() {
        //                         const image_ids = sortableContainer
        //                             .children('.sortable-element')
        //                             .map(function() {
        //                                 return $(this).data('image-id');
        //                             })
        //                             .get();
        //                     },
        //                 });
        //             }
        //         }, 500);

        //         Swal.fire({
        //             title: "Upload Complete",
        //             text: "All images have been uploaded successfully!",
        //             icon: "success",
        //             timer: 1500,
        //             showConfirmButton: false
        //         });
        //     };

        //     processFiles();
        // });



        $(document).on('change', '#add_photo_file', function(event) {
            const files = event.target.files;
            const maxFiles = 5;

            // Count existing previewed images (exclude add_photo_box)
            const existingImages = $('.drag_drop_photo_single').not('.add_photo_box').length;

            // Check total preview limit
            if (existingImages + files.length > maxFiles) {
                Swal.fire({
                    icon: 'error',
                    title: 'Photo Limit Reached',
                    text: `Maximum ${maxFiles} photos allowed per review.`,
                });
                return;
            }

            // Check if any files are selected
            if (files.length === 0) {
                Swal.fire({
                    icon: 'warning',
                    title: 'No Files Selected',
                    text: 'Please select at least one file to upload.',
                });
                return;
            }

            const sortableContainer = $('.drag_drop_photos_wrapper');

            Swal.fire({
                title: "Uploading Images",
                html: `Uploading <b>0</b> out of ${files.length}`,
                allowOutsideClick: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                },
            });

            let uploadedCount = 0;
            const totalFiles = files.length;

            const resizeImage = (file, maxWidth = 600, maxHeight = 600) => {
                return new Promise((resolve) => {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const img = new Image();
                        img.onload = function() {
                            let canvas = document.createElement("canvas");
                            let ctx = canvas.getContext("2d");

                            let ratio = Math.min(maxWidth / img.width, maxHeight / img.height);
                            canvas.width = img.width * ratio;
                            canvas.height = img.height * ratio;

                            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                            resolve(canvas.toDataURL("image/jpeg", 1));
                        };
                        img.src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                });
            };

            const processFiles = async () => {
                // Validation settings
                const validTypes = ['image/jpeg', 'image/jpg', 'image/png', "image/heif", "image/heic"];
                const validExtensions = ['.jpg', '.jpeg', '.png', '.heic', '.heif'];
                const maxSize = 10 * 1024 * 1024; // 10MB
                let validImages = [];
                let validImageCount = 0;

                const getFileExtension = (filename) => {
                    return filename.toLowerCase().substring(filename.lastIndexOf('.'));
                };

                // Helper function to validate file type
                const isValidImageFile = (file) => {
                    const fileExtension = getFileExtension(file.name);
                    
                    // Check MIME type first (if available)
                    if (file.type && validTypes.includes(file.type)) {
                        return true;
                    }
                    
                    // If MIME type is empty or not recognized, check extension
                    if (!file.type || file.type === '') {
                        return validExtensions.includes(fileExtension);
                    }
                    
                    // If MIME type exists but not in validTypes, still check extension as fallback
                    return validExtensions.includes(fileExtension);
                };

                for (let i = 0; i < files.length; i++) {
                    let file = files[i];

                    // Create error message container
                    const errorMessage = $('<div class="error-message"></div>');

                    // Create object URL for file (image preview)
                    const imageUrl = URL.createObjectURL(file);


                    // Validate file type
                    console.log("-------------------------------------------------");
                    console.log("file.type", file.type);
                    console.log("file", file);
                    console.log("-------------------------------------------------");
                    
                    if (!validTypes.includes(file.type)) {
                        errorMessage.text(`❌ Invalid file type: "${file.name}" `);
                        const newDiv = $(`
                        <div class="drag_drop_photo_single sortable-element error">
                            <img alt="Preview ${i}" loading="lazy" src="${imageUrl}">
                            ${errorMessage[0].outerHTML}
                            <div class="delete_btn_wrapper">
                                <a class="delete_btn" href="#"><i class="fa fa-trash" aria-hidden="true"></i></a>
                            </div>
                        </div>
                    `);
                        const addPhotoBox = $('.drag_drop_photo_single.add_photo_box');
                        newDiv.insertBefore(addPhotoBox);
                        continue;
                    }

                    // Validate file size
                    if (file.size > maxSize) {
                        errorMessage.text(`⚠️ File size not allowed: "${file.name}" (must be less than 10MB)`);
                        const newDiv = $(`
                        <div class="drag_drop_photo_single sortable-element warning">
                            <img alt="Preview ${i}" loading="lazy" src="${imageUrl}">
                            ${errorMessage[0].outerHTML}
                            <div class="delete_btn_wrapper">
                                <a class="delete_btn" href="#"><i class="fa fa-trash" aria-hidden="true"></i></a>
                            </div>
                        </div>
                    `);
                        const addPhotoBox = $('.drag_drop_photo_single.add_photo_box');
                        newDiv.insertBefore(addPhotoBox);
                        continue;
                    }

                    // File is valid
                    validImages.push(file);
                    validImageCount++;

                    // Resize and process valid image
                    let resizedDataUrl = await resizeImage(file);

                    uploadedCount++;
                    Swal.update({
                        html: `Uploading <b>${uploadedCount}</b> out of ${totalFiles}...`,
                    });

                    const newDiv = $(`
                        <div class="drag_drop_photo_single sortable-element">
                            <img alt="Preview ${i}" loading="lazy" src="${resizedDataUrl}">
                            <div class="delete_btn_wrapper">
                                <a class="delete_btn" href="#"><i class="fa fa-trash" aria-hidden="true"></i></a>
                            </div>
                        </div>
                    `);

                    const addPhotoBox = $('.drag_drop_photo_single.add_photo_box');
                    newDiv.insertBefore(addPhotoBox);
                }

                // Check if any valid images were processed
                // if (validImageCount === 0) {
                //     Swal.close();
                //     Swal.fire({
                //         icon: 'error',
                //         title: 'No Valid Images',
                //         text: 'No valid images were uploaded.',
                //     });
                //     return;
                // }

                // Initialize Sortable.js
                setTimeout(() => {
                    if (sortableContainer.length && typeof Sortable !== "undefined") {
                        new Sortable(sortableContainer[0], {
                            animation: 150,
                            draggable: '.sortable-element',
                            onEnd: function() {
                                const image_ids = sortableContainer
                                    .children('.sortable-element')
                                    .map(function() {
                                        return $(this).data('image-id');
                                    })
                                    .get();
                            },
                        });
                    }
                }, 500);

                // Show success message
                Swal.fire({
                    title: "Upload Complete",
                    text: `${validImageCount} image(s) uploaded successfully!`,
                    icon: "success",
                    timer: 1500,
                    showConfirmButton: false
                });
            };

            processFiles();
        });




        $(document).on('click',
            '.drag_drop_photos_wrapper .drag_drop_photo_single .delete_btn',
            function() {
                let listing_image = $(this);
                let imageId = listing_image.data('image-id');
                var listing_id = $("input[name='listing_id']").val();

                Swal.fire({
                    title: "Are you sure?",
                    // text: "You won't be able to revert this!",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#ffce32",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Yes, delete it!"
                }).then((result) => {
                    if (result.isConfirmed) {
                        listing_image.closest('.drag_drop_photo_single').remove();
                        Swal.fire({
                            title: "Deleted!",
                            text: "Your file has been deleted.",
                            icon: "success"
                        });
                    }
                });


            });

        window.validateRatings = function() {
            let allRated = true;

            $(".rating_star_wrapper").each(function() {
                if (!$(this).find(".rating-checkbox:checked").length) {
                    allRated = false;
                }
            });

            if (window.commentLength > 0 && window.commentLength < 30) {
                allRated = false;
            }

            $('.review_comment button[type="submit"]').prop("disabled", !allRated);
        }

        // $('#review.modal').on('shown.bs.modal', function() { 
        //     // const postNowBtn = $(".review_comment button[type='submit']");



        //     // Use event delegation to handle dynamically loaded content
        //     $(document).on("change", ".rating-checkbox", validateRatings);

        //     // Ensure validation runs after content is fully loaded
        //     // setTimeout(validateRatings, 500);
        // });
    </script>
@endpush
